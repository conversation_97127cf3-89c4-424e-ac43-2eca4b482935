/**
 * Test component to verify moderator access implementation
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useClubManagementAccess } from '@/hooks/useModeratorPermissions';
import { useAuth } from '@/contexts/AuthContext';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

interface ModeratorAccessTestProps {
  clubId: string;
}

const ModeratorAccessTest: React.FC<ModeratorAccessTestProps> = ({ clubId }) => {
  const { user } = useAuth();
  const {
    canAccessSettings,
    canAccessMembers,
    canAccessModerators,
    canAccessContent,
    canAccessCurrentBook,
    canAccessEvents,
    canAccessAnalytics,
    isClubLead,
    isModerator,
    permissions,
    loading,
    error
  } = useClubManagementAccess(clubId);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Loading Moderator Access Test...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Testing Moderator Access</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  const AccessItem = ({ label, hasAccess }: { label: string; hasAccess: boolean }) => (
    <div className="flex items-center justify-between p-2 border rounded">
      <span>{label}</span>
      <div className="flex items-center gap-2">
        {hasAccess ? (
          <>
            <CheckCircle className="h-4 w-4 text-green-500" />
            <Badge variant="default">Allowed</Badge>
          </>
        ) : (
          <>
            <XCircle className="h-4 w-4 text-red-500" />
            <Badge variant="secondary">Restricted</Badge>
          </>
        )}
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Moderator Access Test - Club {clubId}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Role Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-blue-50 rounded">
            <h4 className="font-semibold">User Role</h4>
            <div className="flex gap-2 mt-1">
              {isClubLead && <Badge variant="default">Club Lead</Badge>}
              {isModerator && <Badge variant="outline">Moderator</Badge>}
              {!isClubLead && !isModerator && <Badge variant="secondary">Regular Member</Badge>}
            </div>
          </div>
          <div className="p-3 bg-gray-50 rounded">
            <h4 className="font-semibold">User ID</h4>
            <p className="text-sm text-gray-600">{user?.id}</p>
          </div>
        </div>

        {/* Moderator Permissions */}
        {isModerator && permissions && (
          <div className="p-3 bg-green-50 rounded">
            <h4 className="font-semibold mb-2">Moderator Permissions</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Analytics: {permissions.analytics_access ? '✅' : '❌'}</div>
              <div>Events: {permissions.meeting_management_access ? '✅' : '❌'}</div>
              <div>Content: {permissions.content_moderation_access ? '✅' : '❌'}</div>
              <div>Members: {permissions.member_management_access ? '✅' : '❌'}</div>
              <div>Customization: {permissions.customization_access ? '✅' : '❌'}</div>
            </div>
          </div>
        )}

        {/* Tab Access Results */}
        <div>
          <h4 className="font-semibold mb-3">Club Management Tab Access</h4>
          <div className="space-y-2">
            <AccessItem label="Overview (Analytics)" hasAccess={canAccessAnalytics} />
            <AccessItem label="Events" hasAccess={canAccessEvents} />
            <AccessItem label="Settings" hasAccess={canAccessSettings} />
            <AccessItem label="Members" hasAccess={canAccessMembers} />
            <AccessItem label="Moderators" hasAccess={canAccessModerators} />
            <AccessItem label="Content" hasAccess={canAccessContent} />
            <AccessItem label="Current Book" hasAccess={canAccessCurrentBook} />
          </div>
        </div>

        {/* Summary */}
        <div className="p-3 bg-yellow-50 rounded">
          <h4 className="font-semibold">Access Summary</h4>
          <p className="text-sm mt-1">
            {isClubLead && "✅ Full access as Club Lead"}
            {isModerator && !isClubLead && `✅ Moderator access to ${
              [canAccessAnalytics, canAccessEvents, canAccessSettings, canAccessMembers, canAccessContent]
                .filter(Boolean).length
            } out of 5 available tabs`}
            {!isClubLead && !isModerator && "❌ No management access"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ModeratorAccessTest;
