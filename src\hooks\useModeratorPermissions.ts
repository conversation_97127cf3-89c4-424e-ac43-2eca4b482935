/**
 * Hook for fetching and managing moderator permissions
 */

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

export interface ModeratorPermissions {
  analytics_access: boolean;
  meeting_management_access: boolean;
  customization_access: boolean;
  content_moderation_access: boolean;
  member_management_access: boolean;
}

export interface UseModeratorPermissionsResult {
  permissions: ModeratorPermissions | null;
  isClubLead: boolean;
  isModerator: boolean;
  loading: boolean;
  error: string | null;
}

/**
 * Hook to fetch moderator permissions for the current user in a specific club
 * Also determines if the user is a club lead or moderator
 */
export function useModeratorPermissions(clubId: string): UseModeratorPermissionsResult {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<ModeratorPermissions | null>(null);
  const [isClubLead, setIsClubLead] = useState(false);
  const [isModerator, setIsModerator] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    async function fetchPermissions() {
      if (!user?.id || !clubId) {
        if (isMounted) {
          setPermissions(null);
          setIsClubLead(false);
          setIsModerator(false);
          setLoading(false);
        }
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Check if user is club lead
        const { data: clubData, error: clubError } = await supabase
          .from('book_clubs')
          .select('lead_user_id')
          .eq('id', clubId)
          .single();

        if (clubError) {
          throw new Error(`Failed to fetch club data: ${clubError.message}`);
        }

        const userIsClubLead = clubData?.lead_user_id === user.id;

        // Check if user is moderator and get permissions
        const { data: moderatorData, error: moderatorError } = await supabase
          .from('club_moderators')
          .select(`
            analytics_access,
            meeting_management_access,
            customization_access,
            content_moderation_access,
            member_management_access
          `)
          .eq('club_id', clubId)
          .eq('user_id', user.id)
          .eq('is_active', true)
          .single();

        if (moderatorError && moderatorError.code !== 'PGRST116') {
          // PGRST116 is "not found" - user is not a moderator, which is fine
          throw new Error(`Failed to fetch moderator data: ${moderatorError.message}`);
        }

        const userIsModerator = !!moderatorData;
        const moderatorPermissions = moderatorData ? {
          analytics_access: moderatorData.analytics_access || false,
          meeting_management_access: moderatorData.meeting_management_access || false,
          customization_access: moderatorData.customization_access || false,
          content_moderation_access: moderatorData.content_moderation_access || false,
          member_management_access: moderatorData.member_management_access || false,
        } : null;

        if (isMounted) {
          setIsClubLead(userIsClubLead);
          setIsModerator(userIsModerator);
          setPermissions(moderatorPermissions);
        }
      } catch (err) {
        console.error('Error fetching moderator permissions:', err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Failed to fetch permissions');
          setPermissions(null);
          setIsClubLead(false);
          setIsModerator(false);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchPermissions();

    return () => {
      isMounted = false;
    };
  }, [user?.id, clubId]);

  return {
    permissions,
    isClubLead,
    isModerator,
    loading,
    error
  };
}

/**
 * Helper hook to check if user has access to a specific club management feature
 */
export function useClubManagementAccess(clubId: string) {
  const { permissions, isClubLead, isModerator, loading, error } = useModeratorPermissions(clubId);

  return {
    // Tab access permissions
    canAccessSettings: isClubLead || (isModerator && permissions?.customization_access),
    canAccessMembers: isClubLead || (isModerator && permissions?.member_management_access),
    canAccessModerators: isClubLead, // Only club leads can manage moderators
    canAccessContent: isClubLead || (isModerator && permissions?.content_moderation_access),
    canAccessCurrentBook: isClubLead, // Only club leads can select books
    canAccessEvents: isClubLead || (isModerator && permissions?.meeting_management_access),
    canAccessAnalytics: isClubLead || (isModerator && permissions?.analytics_access),
    
    // Role information
    isClubLead,
    isModerator,
    permissions,
    loading,
    error
  };
}
